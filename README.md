# Flutter Quill Editor - Bold by Default

A Flutter application that implements a rich text editor using Flutter Quill with bold text as the default formatting. Users can toggle bold formatting on and off as needed.

## Features

- **Bold by Default**: All text is formatted as bold by default
- **Toggle Bold**: Users can toggle bold formatting on/off using the bold button
- **Bold Default Switch**: A switch to enable/disable bold as the default formatting
- **Rich Text Formatting**: Support for italic, underline, and other formatting options
- **Custom Toolbar**: Clean, intuitive toolbar with formatting options
- **Clear and Reset**: Floating action button to clear all text and reset bold formatting

## Getting Started

### Prerequisites

- Flutter SDK (latest stable version)
- Dart SDK
- iOS Simulator, Android Emulator, or physical device

### Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd quill_editor
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the application:
```bash
flutter run
```

## Usage

### Basic Usage

1. **Start Typing**: When you open the app, you'll see placeholder text that's already bold
2. **Bold by Default**: All new text you type will be bold automatically
3. **Toggle Bold**: Click the bold button (B) in the toolbar to toggle bold formatting for selected text
4. **Bold Default Switch**: Use the switch in the toolbar to enable/disable bold as the default for new text

### Features Explained

#### Bold by Default Behavior
- When the app starts, the initial text is formatted as bold
- New text typed will automatically be bold when "Bold by default" is enabled
- The switch in the toolbar controls this behavior

#### Formatting Options
- **Bold**: Toggle bold formatting (default: enabled)
- **Italic**: Apply italic formatting to selected text
- **Underline**: Apply underline formatting to selected text

#### Clear and Reset
- The floating action button (refresh icon) clears all text and resets bold formatting

## Implementation Details

### Key Components

1. **QuillController**: Manages the document and text selection
2. **QuillEditor.basic**: The main text editor widget
3. **Custom Toolbar**: Custom-built toolbar with formatting buttons and bold default switch
4. **Bold Default Logic**: Automatically applies bold formatting to new text when enabled

### Code Structure

```dart
class _QuillEditorPageState extends State<QuillEditorPage> {
  late QuillController _controller;
  bool _isBoldDefault = true;

  // Initialize with bold formatting
  void _setBoldAsDefault() {
    // Apply bold to all existing text
    // Set cursor position
  }

  // Toggle bold formatting
  void _toggleBold() {
    _controller.formatSelection(Attribute.bold);
  }

  // Toggle bold default behavior
  void _toggleBoldDefault() {
    setState(() {
      _isBoldDefault = !_isBoldDefault;
    });
  }
}
```

## Dependencies

- `flutter_quill: ^11.4.2` - Rich text editor for Flutter
- `flutter_localizations` - Localization support

## Testing

Run the tests with:
```bash
flutter test
```

The test suite includes:
- Widget existence tests
- Bold default switch functionality
- Formatting button availability
- Clear and reset functionality

## Troubleshooting

### Common Issues

1. **Localization Errors**: If you encounter localization errors, ensure `flutter_localizations` is properly configured
2. **Bold Not Applying**: Make sure the `_isBoldDefault` flag is set to `true`
3. **Layout Issues**: Ensure proper use of `SafeArea` and `Expanded` widgets

## License

This project is licensed under the MIT License.
