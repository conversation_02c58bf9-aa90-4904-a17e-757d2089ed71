import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Quill Editor Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('en', 'US')],
      home: const QuillEditorPage(),
    );
  }
}

class QuillEditorPage extends StatefulWidget {
  const QuillEditorPage({super.key});

  @override
  State<QuillEditorPage> createState() => _QuillEditorPageState();
}

class _QuillEditorPageState extends State<QuillEditorPage> {
  late QuillController _controller;

  @override
  void initState() {
    super.initState();

    // Create a document with some initial text
    final doc = Document()
      ..insert(0, 'Type here... Bold is enabled by default!');

    // Initialize the controller with the document
    _controller = QuillController(
      document: doc,
      selection: const TextSelection.collapsed(offset: 0),
    );

    // Set bold as the default formatting after initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setBoldAsDefault();
    });
  }

  void _setBoldAsDefault() {
    // Select all text and apply bold formatting
    _controller.updateSelection(
      TextSelection(
        baseOffset: 0,
        extentOffset: _controller.document.length - 1,
      ),
      ChangeSource.local,
    );
    _controller.formatSelection(Attribute.bold);

    // Move cursor to the end
    _controller.updateSelection(
      TextSelection.collapsed(offset: _controller.document.length - 1),
      ChangeSource.local,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('Quill Editor - Bold by Default'),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Custom Quill Toolbar
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Wrap(
                spacing: 4.0,
                children: [
                  // Bold button
                  IconButton(
                    icon: const Icon(Icons.format_bold),
                    onPressed: () =>
                        _controller.formatSelection(Attribute.bold),
                    tooltip: 'Bold',
                    color: Colors.blue,
                  ),
                  // Italic button
                  IconButton(
                    icon: const Icon(Icons.format_italic),
                    onPressed: () =>
                        _controller.formatSelection(Attribute.italic),
                    tooltip: 'Italic',
                  ),
                  // Underline button
                  IconButton(
                    icon: const Icon(Icons.format_underlined),
                    onPressed: () =>
                        _controller.formatSelection(Attribute.underline),
                    tooltip: 'Underline',
                  ),
                  // Strikethrough button
                  IconButton(
                    icon: const Icon(Icons.strikethrough_s),
                    onPressed: () =>
                        _controller.formatSelection(Attribute.strikeThrough),
                    tooltip: 'Strikethrough',
                  ),
                  const SizedBox(width: 8),
                  // List buttons
                  IconButton(
                    icon: const Icon(Icons.format_list_bulleted),
                    onPressed: () => _controller.formatSelection(Attribute.ul),
                    tooltip: 'Bullet List',
                  ),
                  IconButton(
                    icon: const Icon(Icons.format_list_numbered),
                    onPressed: () => _controller.formatSelection(Attribute.ol),
                    tooltip: 'Numbered List',
                  ),
                  const SizedBox(width: 8),
                  // Quote button
                  IconButton(
                    icon: const Icon(Icons.format_quote),
                    onPressed: () =>
                        _controller.formatSelection(Attribute.blockQuote),
                    tooltip: 'Quote',
                  ),
                  // Code block button
                  IconButton(
                    icon: const Icon(Icons.code),
                    onPressed: () =>
                        _controller.formatSelection(Attribute.codeBlock),
                    tooltip: 'Code Block',
                  ),
                ],
              ),
            ),
            // Editor
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16.0),
                child: QuillEditor.basic(controller: _controller),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Clear all text and reset with bold formatting
          _controller.clear();
          _setBoldAsDefault();
        },
        tooltip: 'Clear and Reset Bold',
        child: const Icon(Icons.refresh),
      ),
    );
  }
}
