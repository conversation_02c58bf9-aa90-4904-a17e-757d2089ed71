import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Quill Editor Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const QuillEditorPage(),
    );
  }
}

class QuillEditorPage extends StatefulWidget {
  const QuillEditorPage({super.key});

  @override
  State<QuillEditorPage> createState() => _QuillEditorPageState();
}

class _QuillEditorPageState extends State<QuillEditorPage> {
  late QuillController _controller;
  bool _isBoldDefault = true;

  @override
  void initState() {
    super.initState();

    // Create a document with some initial text
    final doc = Document()
      ..insert(0, 'Type here... Bold is enabled by default!');

    // Initialize the controller with the document
    _controller = QuillController(
      document: doc,
      selection: const TextSelection.collapsed(offset: 0),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('Quill Editor - Bold by Default'),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Custom Toolbar
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.format_italic),
                    onPressed: () =>
                        _controller.formatSelection(Attribute.italic),
                    tooltip: 'Italic',
                  ),
                  IconButton(
                    icon: const Icon(Icons.format_underlined),
                    onPressed: () =>
                        _controller.formatSelection(Attribute.underline),
                    tooltip: 'Underline',
                  ),
                  const VerticalDivider(),

                  const SizedBox(width: 8),
                  Text(
                    'Bold by default',
                    style: TextStyle(
                      color: _isBoldDefault
                          ? Colors.blue
                          : Colors.grey.shade600,
                      fontSize: 12,
                      fontWeight: _isBoldDefault
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
            // Editor
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16.0),
                child: QuillEditor.basic(controller: _controller),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Clear all text and reset with bold formatting
          _controller.clear();
        },
        tooltip: 'Clear and Reset Bold',
        child: const Icon(Icons.refresh),
      ),
    );
  }
}
