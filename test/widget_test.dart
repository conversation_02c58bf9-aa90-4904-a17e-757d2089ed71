import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:quill_editor/main.dart';

void main() {
  group('Quill Editor Tests', () {
    testWidgets('App should load with QuillEditorPage', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());

      // Verify that the app loads with the correct title
      expect(find.text('Quill Editor - Bold by Default'), findsOneWidget);

      // Verify that the bold toggle button exists
      expect(find.byIcon(Icons.format_bold), findsWidgets);

      // Verify that the "Bold by default" switch exists
      expect(find.text('Bold by default'), findsOneWidget);

      // Verify that the switch is initially on (bold is default)
      final switchWidget = tester.widget<Switch>(find.byType(Switch));
      expect(switchWidget.value, isTrue);
    });

    testWidgets('Bold default switch should toggle', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());

      // Find the switch
      final switchFinder = find.byType(Switch);
      expect(switchFinder, findsOneWidget);

      // Verify initial state is true
      Switch switchWidget = tester.widget<Switch>(switchFinder);
      expect(switchWidget.value, isTrue);

      // Tap the switch to toggle it
      await tester.tap(switchFinder);
      await tester.pump();

      // Verify the switch is now false
      switchWidget = tester.widget<Switch>(switchFinder);
      expect(switchWidget.value, isFalse);
    });

    testWidgets('Formatting buttons should exist', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      // Verify formatting buttons exist
      expect(find.byIcon(Icons.format_bold), findsWidgets);
      expect(find.byIcon(Icons.format_italic), findsOneWidget);
      expect(find.byIcon(Icons.format_underlined), findsOneWidget);
    });

    testWidgets('Clear and reset button should exist', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());

      // Verify the floating action button exists
      expect(find.byIcon(Icons.refresh), findsOneWidget);

      // Verify the tooltip
      final fab = find.byType(FloatingActionButton);
      expect(fab, findsOneWidget);
    });
  });
}
